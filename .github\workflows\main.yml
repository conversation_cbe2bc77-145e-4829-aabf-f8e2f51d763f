name: Web Build + Deployment to GitHub Pages 

on:
  # Runs on push to any of the below branches
  push:
    branches:
      - main

  # Runs on pull request events that target one of the below branches
  pull_request:
    branches:
      - main

  # Allows you to run this workflow manually from the Actions tab of the repository
  workflow_dispatch:

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

env:
  # https://flet.dev/docs/publish#versioning
  BUILD_NUMBER: 1
  BUILD_VERSION: 1.0.0

  # Python version to use
  PYTHON_VERSION: 3.12.8

  # flet-cli version to install for `flet build`
  FLET_CLI_VERSION: 0.27.5

  # Ensures Python uses UTF-8 encoding by default
  PYTHONUTF8: 1

  # Disables rich text formatting in Flet CLI output
  FLET_CLI_NO_RICH_OUTPUT: 1

  # Disables progress bars when using UV
  UV_NO_PROGRESS: 1

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install flet-cli ${{ env.FLET_CLI_VERSION }}
      run: |
        python -m pip install --upgrade pip
        pip install flet-cli==$FLET_CLI_VERSION

    - name: Flet Build Web
      run: |
        echo "GITHUB_REPOSITORY: ${GITHUB_REPOSITORY}, USER: ${GITHUB_REPOSITORY%/*}, PROJECT_BASE_URL: ${GITHUB_REPOSITORY#*/}"
        flet build web --base-url ${GITHUB_REPOSITORY#*/} --route-url-strategy hash

    - name: Upload Artifact
      uses: actions/upload-pages-artifact@v3
      with:
        name: web-build-artifact  # the name of the artifact
        path: build/web

  deploy:
    needs: build  # wait for the "build" job to get done before executing this "deploy" job

    runs-on: ubuntu-latest

    # Grant GITHUB_TOKEN the permissions required to make a Pages deployment
    permissions:
      pages: write      # to deploy to Pages
      id-token: write   # to verify the deployment originates from an appropriate source

    # Deploy to the github-pages environment
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
      
    steps:
      - name: Setup Pages
        uses: actions/configure-pages@v5
        
      - name: Deploy to GitHub Pages 🚀
        if: github.event_name == 'push'  # deploy only on push
        id: deployment
        uses: actions/deploy-pages@v4.0.5
        with:
          artifact_name: web-build-artifact
