#!/usr/bin/env python3
"""
Simple Demo Flask App for Fabric Defect Detection
This version runs without complex ML dependencies for demonstration
"""

from flask import Flask, render_template, request, jsonify
import os
import base64
import tempfile
import json

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/camera')
def camera():
    """Camera detection page"""
    return render_template('camera.html')

@app.route('/detect', methods=['POST'])
def detect_defects():
    """Handle image upload and defect detection - DEMO MODE"""
    if 'image' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400
    
    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    try:
        # Read the uploaded file
        file_bytes = file.read()
        
        # Convert to base64 for demo (just return the original image)
        result_base64 = base64.b64encode(file_bytes).decode('utf-8')
        
        # Create demo detection results
        demo_detections = [
            {
                'class': 'hole',
                'confidence': 0.85,
                'bbox': (100, 100, 200, 200)
            },
            {
                'class': 'stain',
                'confidence': 0.72,
                'bbox': (300, 150, 400, 250)
            }
        ]
        
        # Count defects by type
        defect_counts = {}
        for det in demo_detections:
            defect_type = det['class'].lower()
            defect_counts[defect_type] = defect_counts.get(defect_type, 0) + 1
        
        return jsonify({
            'success': True,
            'result_image': result_base64,
            'detections': demo_detections,
            'total_defects': len(demo_detections),
            'defect_counts': defect_counts,
            'confidence_threshold': 0.4,
            'demo_mode': True,
            'message': 'Demo mode: Showing sample defect detection results'
        })
        
    except Exception as e:
        return jsonify({'error': f'Detection failed: {str(e)}'}), 500

@app.route('/update_confidence', methods=['POST'])
def update_confidence():
    """Update confidence threshold - DEMO MODE"""
    try:
        data = request.get_json()
        new_confidence = float(data.get('confidence', 0.4))
        
        # Clamp confidence between 0.05 and 0.95
        new_confidence = max(0.05, min(0.95, new_confidence))
        
        return jsonify({
            'success': True,
            'confidence': new_confidence,
            'demo_mode': True
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to update confidence: {str(e)}'}), 500

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'detector_initialized': False,
        'demo_mode': True,
        'message': 'Running in demo mode - no ML dependencies required'
    })

if __name__ == '__main__':
    print("🧵 Fabric Defect Detection - Demo Mode")
    print("=" * 50)
    print("✅ Starting Flask application in DEMO mode...")
    print("📝 Note: This demo shows the interface without ML processing")
    print("🌐 Open your browser to: http://localhost:5000")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
